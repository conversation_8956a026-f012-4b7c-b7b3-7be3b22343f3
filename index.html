<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snake Game</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(45deg, #0f0f23, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            font-family: 'Orbitron', monospace;
            color: white;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .game-container {
            text-align: center;
            background: rgba(0, 0, 0, 0.4);
            padding: 30px;
            border-radius: 25px;
            box-shadow:
                0 0 50px rgba(0, 255, 255, 0.3),
                0 0 100px rgba(0, 255, 255, 0.1),
                inset 0 0 50px rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(0, 255, 255, 0.3);
            position: relative;
            animation: containerGlow 3s ease-in-out infinite alternate;
        }

        @keyframes containerGlow {
            0% { box-shadow: 0 0 50px rgba(0, 255, 255, 0.3), 0 0 100px rgba(0, 255, 255, 0.1), inset 0 0 50px rgba(255, 255, 255, 0.05); }
            100% { box-shadow: 0 0 70px rgba(0, 255, 255, 0.5), 0 0 120px rgba(0, 255, 255, 0.2), inset 0 0 50px rgba(255, 255, 255, 0.1); }
        }

        h1 {
            margin: 0 0 20px 0;
            font-size: 3em;
            font-weight: 900;
            text-shadow:
                0 0 10px #00ffff,
                0 0 20px #00ffff,
                0 0 30px #00ffff;
            animation: titlePulse 2s ease-in-out infinite alternate;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        @keyframes titlePulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.05); }
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        #gameCanvas {
            border: 4px solid transparent;
            border-radius: 15px;
            box-shadow:
                0 0 30px rgba(0, 255, 255, 0.6),
                0 0 60px rgba(0, 255, 255, 0.3),
                inset 0 0 30px rgba(0, 0, 0, 0.5);
            background: radial-gradient(circle at center, #0a0a0a, #000000);
            position: relative;
            animation: canvasGlow 2s ease-in-out infinite alternate;
        }

        @keyframes canvasGlow {
            0% { box-shadow: 0 0 30px rgba(0, 255, 255, 0.6), 0 0 60px rgba(0, 255, 255, 0.3), inset 0 0 30px rgba(0, 0, 0, 0.5); }
            100% { box-shadow: 0 0 40px rgba(0, 255, 255, 0.8), 0 0 80px rgba(0, 255, 255, 0.4), inset 0 0 30px rgba(0, 0, 0, 0.5); }
        }

        .controls {
            margin-top: 20px;
            font-size: 1.1em;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.6);
        }

        .controls p {
            margin: 8px 0;
            opacity: 0.9;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            padding: 50px;
            border-radius: 20px;
            text-align: center;
            display: none;
            z-index: 100;
            border: 3px solid rgba(255, 0, 0, 0.5);
            box-shadow:
                0 0 50px rgba(255, 0, 0, 0.5),
                0 0 100px rgba(255, 0, 0, 0.2);
            animation: gameOverPulse 1s ease-in-out infinite alternate;
        }

        @keyframes gameOverPulse {
            0% { transform: translate(-50%, -50%) scale(1); }
            100% { transform: translate(-50%, -50%) scale(1.02); }
        }

        .game-over h2 {
            color: #ff4757;
            font-size: 3em;
            margin: 0 0 20px 0;
            font-weight: 900;
            text-shadow:
                0 0 10px #ff4757,
                0 0 20px #ff4757,
                0 0 30px #ff4757;
            animation: errorGlow 1.5s ease-in-out infinite alternate;
        }

        @keyframes errorGlow {
            0% { text-shadow: 0 0 10px #ff4757, 0 0 20px #ff4757, 0 0 30px #ff4757; }
            100% { text-shadow: 0 0 15px #ff4757, 0 0 30px #ff4757, 0 0 45px #ff4757; }
        }

        .game-over p {
            font-size: 1.4em;
            margin: 15px 0;
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
        }

        .restart-btn {
            background: linear-gradient(45deg, #2ed573, #17a2b8);
            color: white;
            border: none;
            padding: 18px 35px;
            font-size: 1.3em;
            font-weight: 700;
            border-radius: 12px;
            cursor: pointer;
            margin-top: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(46, 213, 115, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'Orbitron', monospace;
        }

        .restart-btn:hover {
            background: linear-gradient(45deg, #26d467, #138496);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 213, 115, 0.6);
        }

        .speed-selector {
            margin-bottom: 25px;
        }

        .speed-selector select {
            padding: 12px 20px;
            font-size: 1.1em;
            border-radius: 10px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            background: rgba(0, 0, 0, 0.3);
            color: #00ffff;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .speed-selector select:hover {
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        .speed-selector option {
            background: #1a1a2e;
            color: #00ffff;
            font-weight: 700;
        }

        .speed-selector label {
            font-weight: 700;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.8);
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 Snake Game</h1>
        
        <div class="speed-selector">
            <label for="speed">Speed: </label>
            <select id="speed">
                <option value="200">Easy</option>
                <option value="150" selected>Normal</option>
                <option value="100">Hard</option>
                <option value="50">Extreme</option>
            </select>
        </div>

        <div class="game-info">
            <div>Score: <span id="score">0</span></div>
            <div>High Score: <span id="highScore">0</span></div>
        </div>

        <canvas id="gameCanvas" width="600" height="600"></canvas>

        <div class="controls">
            <p>🎮 Use Arrow Keys or WASD to control the snake</p>
            <p>🍎 Eat food to grow and increase your score</p>
            <p>⚡ Avoid hitting walls or yourself!</p>
        </div>

        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <p id="newHighScore" style="color: #2ed573; display: none;">🎉 New High Score!</p>
            <button class="restart-btn" onclick="restartGame()">Play Again</button>
        </div>
    </div>

    <script src="snake.js"></script>
</body>
</html>
