<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snake Game</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Inter', sans-serif;
            color: white;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            margin: 0 0 20px 0;
            font-size: 2.5em;
            font-weight: 700;
            color: white;
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: 600;
        }

        #gameCanvas {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            background: #1a1a1a;
        }

        .controls {
            margin-top: 20px;
            font-size: 1.1em;
        }

        .controls p {
            margin: 8px 0;
            opacity: 0.9;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            display: none;
            z-index: 100;
            border: 2px solid #ff4757;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .game-over h2 {
            color: #ff4757;
            font-size: 2.5em;
            margin: 0 0 20px 0;
            font-weight: 700;
        }

        .game-over p {
            font-size: 1.3em;
            margin: 10px 0;
        }

        .restart-btn {
            background: #2ed573;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
            font-family: 'Inter', sans-serif;
        }

        .restart-btn:hover {
            background: #26d467;
            transform: translateY(-1px);
        }

        .speed-selector {
            margin-bottom: 20px;
        }

        .speed-selector select {
            padding: 10px 15px;
            font-size: 1em;
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
        }

        .speed-selector select:hover {
            border-color: rgba(255, 255, 255, 0.5);
        }

        .speed-selector option {
            background: #333;
            color: white;
        }

        .speed-selector label {
            font-weight: 600;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 Snake Game</h1>
        
        <div class="speed-selector">
            <label for="speed">Speed: </label>
            <select id="speed">
                <option value="200">Easy</option>
                <option value="150" selected>Normal</option>
                <option value="100">Hard</option>
                <option value="50">Extreme</option>
            </select>
        </div>

        <div class="game-info">
            <div>Score: <span id="score">0</span></div>
            <div>High Score: <span id="highScore">0</span></div>
        </div>

        <canvas id="gameCanvas" width="600" height="600"></canvas>

        <div class="controls">
            <p>🎮 Use Arrow Keys or WASD to control the snake</p>
            <p>🍎 Eat food to grow and increase your score</p>
            <p>⚡ Avoid hitting walls or yourself!</p>
        </div>

        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <p id="newHighScore" style="color: #2ed573; display: none;">🎉 New High Score!</p>
            <button class="restart-btn" onclick="restartGame()">Play Again</button>
        </div>
    </div>

    <script src="snake.js"></script>
</body>
</html>
