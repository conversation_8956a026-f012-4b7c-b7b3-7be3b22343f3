const readline = require('readline');

const readline = require('readline');

const BOARD_WIDTH = 20;
const BOARD_HEIGHT = 20; 
const SNAKE_INITIAL_LENGTH = 5;
const FOOD_SPAWN_RATE = 0.1;

const board = Array(BOARD_HEIGHT).fill(0).map(() => Array(BOARD_WIDTH).fill(''));

let snake = [];
let food = null;

function drawBoard() {
  console.log('  ' + Array(BOARD_WIDTH + 1).join('-'));
  for (let i = 0; i < BOARD_HEIGHT; i++) {
    let row = '| ';
    for (let j = 0; j < BOARD_WIDTH; j++) {
      if (snake.includes(`${i},${j}`)) {
        row += 'S ';
      } else if (food && food.x === j && food.y === i) {
        row += 'F ';
      } else {
        row += '  ';
      }
    }
    row += '|';
    console.log(row);
  }
  console.log('  ' + Array(BOARD_WIDTH + 1).join('-'));
}

function updateGameState() {
  const head = snake[0];
  const newHead = {
    x: head.x + (Math.random() < 0.5 ? -1 : 1),
    y: head.y + (Math.random() < 0.5 ? -1 : 1)
  };
  snake.unshift(newHead);

  if (newHead.x < 0 || newHead.x >= BOARD_WIDTH || newHead.y < 0 || newHead.y >= BOARD_HEIGHT || snake.includes(`${newHead.y},${newHead.x}`)) {
    console.log('Game over!');
    process.exit();
  }

  if (food && food.x === newHead.x && food.y === newHead.y) {
    food = null;
  } else {
    snake.pop();
  }

  if (Math.random() < FOOD_SPAWN_RATE) {
    food = {
      x: Math.floor(Math.random() * BOARD_WIDTH),
      y: Math.floor(Math.random() * BOARD_HEIGHT)
    };
  }
}

function handleInput(input) {
  switch (input.trim().toUpperCase()) {
    case 'W':
      snake[0].y -= 1;
      break;
    case 'A':
      snake[0].x -= 1;
      break;
    case 'S':
      snake[0].y += 1;
      break;
    case 'D':
      snake[0].x += 1;
      break;
    default:
      console.log('Invalid input!');
  }
}

function gameLoop() {
  drawBoard();
  readline.question('Enter a direction (W, A, S, D): ', handleInput);
  updateGameState();
  gameLoop();
}

gameLoop();

const BOARD_WIDTH = 20;
const BOARD_HIGHT = 20; 
const SNAKE_INITIAL_LENGTH = 5;
const FOOD_SPAWN_RATE = 0.1;

const board = Array(BOARD_HIGHT ).fill(0).map(() => Array(BOARD_WIDTH).fill(''));

let snake = [];
let food = null;

function updateGameState() {
    const head = snake[0];
    const newHead = {
      x: head.x + (Math.random() < 0.5 ? -1 : 1),
      y: head.y + (Math.random() < 0.5 ? -1 : 1)
    };
    snake.unshift(newHead);
  
    if (newHead.x < 0 || newHead.x >= BOARD_WIDTH || newHead.y < 0 || newHead.y >= BOARD_HEIGHT || snake.includes(`${newHead.y},${newHead.x}`)) {
      console.log('Game over!');
      process.exit();
    }
  
    if (food && food.x === newHead.x && food.y === newHead.y) {
      food = null;
    } else {
      snake.pop();
    }

    if (Math.random() < FOOD_SPAWN_RATE) {
      food = {
        x: Math.floor(Math.random() * BOARD_WIDTH),
        y: Math.floor(Math.random() * BOARD_HEIGHT)
      };
    }
  }

function handleInput(input) {
    switch (input.trim().toUpperCase()) {
      case 'W':
        snake[0].y -= 1;
        break;
      case 'A':
        snake[0].x -= 1;
        break;
      case 'S':
        snake[0].y += 1;
        break;
      case 'D':
        snake[0].x += 1;
        break;
      default:
        console.log('Invalid input!');
    }
  }

function gameLoop() {
    drawBoard();
    readline.question('Enter a direction (W, A, S, D): ', handleInput);
    updateGameState();
    gameLoop();
  }
  
  gameLoop();

function drawBoard() {
  console.log('  ' + Array(BOARD_WIDTH + 1).join('-'));
  for (let i = 0; i < BOARD_HEIGHT; i++) {
    let row = '| ';
    for (let j = 0; j < BOARD_WIDTH; j++) {
      if (snake.includes(`${i},${j}`)) {
        row += 'S ';
      } else if (food && food.x === j && food.y === i) {
        row += 'F ';
      } else {
        row += '  ';
      }
    }
    row += '|';
    console.log(row);
  }
  console.log('  ' + Array(BOARD_WIDTH + 1).join('-'));
}

  