// Game constants
const BOARD_WIDTH = 20;
const BOARD_HEIGHT = 20;
const SNAKE_INITIAL_LENGTH = 3;

// Game state
let snake = [];
let food = null;
let direction = { x: 1, y: 0 }; // Start moving right
let gameRunning = true;
let score = 0;

// Initialize the snake in the center of the board
function initializeSnake() {
  const centerX = Math.floor(BOARD_WIDTH / 2);
  const centerY = Math.floor(BOARD_HEIGHT / 2);

  snake = [];
  for (let i = 0; i < SNAKE_INITIAL_LENGTH; i++) {
    snake.push({ x: centerX - i, y: centerY });
  }
}

// Generate food at a random empty position
function generateFood() {
  let newFood;
  do {
    newFood = {
      x: Math.floor(Math.random() * BOARD_WIDTH),
      y: Math.floor(Math.random() * BOARD_HEIGHT)
    };
  } while (isSnakePosition(newFood.x, newFood.y));

  food = newFood;
}

// Check if a position is occupied by the snake
function isSnakePosition(x, y) {
  return snake.some(segment => segment.x === x && segment.y === y);
}

// Draw the game board
function drawBoard() {
  console.clear();
  console.log(`Score: ${score}`);
  console.log('Use WASD to control the snake, Q to quit');
  console.log('');

  // Top border
  console.log('┌' + '─'.repeat(BOARD_WIDTH * 2) + '┐');

  for (let y = 0; y < BOARD_HEIGHT; y++) {
    let row = '│';
    for (let x = 0; x < BOARD_WIDTH; x++) {
      if (snake[0].x === x && snake[0].y === y) {
        row += '🐍'; // Snake head
      } else if (isSnakePosition(x, y)) {
        row += '██'; // Snake body
      } else if (food && food.x === x && food.y === y) {
        row += '🍎'; // Food
      } else {
        row += '  '; // Empty space
      }
    }
    row += '│';
    console.log(row);
  }

  // Bottom border
  console.log('└' + '─'.repeat(BOARD_WIDTH * 2) + '┘');
}

// Update game state
function updateGameState() {
  if (!gameRunning) return;

  const head = snake[0];
  const newHead = {
    x: head.x + direction.x,
    y: head.y + direction.y
  };

  // Check wall collision
  if (newHead.x < 0 || newHead.x >= BOARD_WIDTH ||
      newHead.y < 0 || newHead.y >= BOARD_HEIGHT) {
    gameOver();
    return;
  }

  // Check self collision
  if (isSnakePosition(newHead.x, newHead.y)) {
    gameOver();
    return;
  }

  snake.unshift(newHead);

  // Check food collision
  if (food && food.x === newHead.x && food.y === newHead.y) {
    score += 10;
    generateFood();
  } else {
    snake.pop(); // Remove tail if no food eaten
  }
}

// Handle user input
function handleInput(key) {
  if (!gameRunning) return;

  switch (key.toUpperCase()) {
    case 'W':
      if (direction.y !== 1) direction = { x: 0, y: -1 }; // Up
      break;
    case 'S':
      if (direction.y !== -1) direction = { x: 0, y: 1 }; // Down
      break;
    case 'A':
      if (direction.x !== 1) direction = { x: -1, y: 0 }; // Left
      break;
    case 'D':
      if (direction.x !== -1) direction = { x: 1, y: 0 }; // Right
      break;
    case 'Q':
      console.log('\nThanks for playing!');
      process.exit(0);
  }
}

// Game over function
function gameOver() {
  gameRunning = false;
  console.log('\n🎮 GAME OVER! 🎮');
  console.log(`Final Score: ${score}`);
  console.log('\nPress R to restart or Q to quit');
}

// Restart game
function restartGame() {
  gameRunning = true;
  score = 0;
  direction = { x: 1, y: 0 };
  initializeSnake();
  generateFood();
}

// Main game loop
function gameLoop() {
  if (gameRunning) {
    updateGameState();
    drawBoard();
  }

  setTimeout(gameLoop, 200); // Game speed (200ms per frame)
}

// Set up input handling
process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.setEncoding('utf8');

process.stdin.on('data', (key) => {
  if (gameRunning) {
    handleInput(key);
  } else {
    // Handle restart/quit when game is over
    switch (key.toUpperCase()) {
      case 'R':
        restartGame();
        break;
      case 'Q':
        console.log('\nThanks for playing!');
        process.exit(0);
        break;
    }
  }
});

// Initialize and start the game
console.log('🐍 Welcome to Snake Game! 🐍');
console.log('Loading...');

initializeSnake();
generateFood();
setTimeout(() => {
  drawBoard();
  gameLoop();
}, 1000);