// Game constants
const GRID_SIZE = 20;
const CANVAS_SIZE = 600;
const INITIAL_SNAKE_LENGTH = 3;

// Game state
let canvas, ctx;
let snake = [];
let food = {};
let direction = { x: 1, y: 0 };
let gameRunning = false;
let score = 0;
let highScore = 0;
let gameSpeed = 150;
let gameLoop;

// Initialize the game
function initGame() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');

    // Load high score from localStorage
    highScore = localStorage.getItem('snakeHighScore') || 0;
    document.getElementById('highScore').textContent = highScore;

    // Set up event listeners
    document.addEventListener('keydown', handleKeyPress);
    document.getElementById('speed').addEventListener('change', changeSpeed);

    resetGame();
    startGame();
}

// Reset game to initial state
function resetGame() {
    // Initialize snake in center
    snake = [];
    const centerX = Math.floor(CANVAS_SIZE / GRID_SIZE / 2);
    const centerY = Math.floor(CANVAS_SIZE / GRID_SIZE / 2);

    for (let i = 0; i < INITIAL_SNAKE_LENGTH; i++) {
        snake.push({ x: centerX - i, y: centerY });
    }

    direction = { x: 1, y: 0 };
    score = 0;
    updateScore();
    generateFood();
    gameRunning = true;

    // Hide game over screen
    document.getElementById('gameOver').style.display = 'none';
}

// Generate food at random position
function generateFood() {
    do {
        food = {
            x: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE)),
            y: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE))
        };
    } while (isSnakePosition(food.x, food.y));
}

// Check if position is occupied by snake
function isSnakePosition(x, y) {
    return snake.some(segment => segment.x === x && segment.y === y);
}

// Update game state
function update() {
    if (!gameRunning) return;

    const head = { ...snake[0] };
    head.x += direction.x;
    head.y += direction.y;

    // Check wall collision
    if (head.x < 0 || head.x >= CANVAS_SIZE / GRID_SIZE ||
        head.y < 0 || head.y >= CANVAS_SIZE / GRID_SIZE) {
        endGame();
        return;
    }

    // Check self collision
    if (isSnakePosition(head.x, head.y)) {
        endGame();
        return;
    }

    snake.unshift(head);

    // Check food collision
    if (head.x === food.x && head.y === food.y) {
        score += 10;
        updateScore();
        generateFood();

        // Increase speed slightly as score increases
        if (score % 50 === 0 && gameSpeed > 50) {
            gameSpeed = Math.max(50, gameSpeed - 5);
            clearInterval(gameLoop);
            startGameLoop();
        }
    } else {
        snake.pop();
    }
}

// Render the game
function draw() {
    // Clear canvas
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    // Draw grid (optional, subtle)
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 0.5;
    for (let i = 0; i <= CANVAS_SIZE; i += GRID_SIZE) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, CANVAS_SIZE);
        ctx.moveTo(0, i);
        ctx.lineTo(CANVAS_SIZE, i);
        ctx.stroke();
    }

    // Draw snake
    snake.forEach((segment, index) => {
        if (index === 0) {
            // Snake head - brighter green
            ctx.fillStyle = '#4ade80';
        } else {
            // Snake body - darker green
            ctx.fillStyle = '#22c55e';
        }

        ctx.fillRect(
            segment.x * GRID_SIZE + 1,
            segment.y * GRID_SIZE + 1,
            GRID_SIZE - 2,
            GRID_SIZE - 2
        );

        // Add some styling to head
        if (index === 0) {
            ctx.fillStyle = '#16a34a';
            ctx.fillRect(
                segment.x * GRID_SIZE + 4,
                segment.y * GRID_SIZE + 4,
                GRID_SIZE - 8,
                GRID_SIZE - 8
            );
        }
    });

    // Draw food
    ctx.fillStyle = '#ef4444';
    ctx.fillRect(
        food.x * GRID_SIZE + 2,
        food.y * GRID_SIZE + 2,
        GRID_SIZE - 4,
        GRID_SIZE - 4
    );

    // Add food highlight
    ctx.fillStyle = '#fca5a5';
    ctx.fillRect(
        food.x * GRID_SIZE + 6,
        food.y * GRID_SIZE + 6,
        GRID_SIZE - 12,
        GRID_SIZE - 12
    );
}

// Handle keyboard input
function handleKeyPress(event) {
    if (!gameRunning) return;

    const key = event.key.toLowerCase();

    // Prevent default behavior for arrow keys
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        event.preventDefault();
    }

    switch (key) {
        case 'arrowup':
        case 'w':
            if (direction.y !== 1) direction = { x: 0, y: -1 };
            break;
        case 'arrowdown':
        case 's':
            if (direction.y !== -1) direction = { x: 0, y: 1 };
            break;
        case 'arrowleft':
        case 'a':
            if (direction.x !== 1) direction = { x: -1, y: 0 };
            break;
        case 'arrowright':
        case 'd':
            if (direction.x !== -1) direction = { x: 1, y: 0 };
            break;
    }
}

// Change game speed
function changeSpeed() {
    gameSpeed = parseInt(document.getElementById('speed').value);
    if (gameRunning) {
        clearInterval(gameLoop);
        startGameLoop();
    }
}

// Update score display
function updateScore() {
    document.getElementById('score').textContent = score;
}

// End game
function endGame() {
    gameRunning = false;
    clearInterval(gameLoop);

    // Check for new high score
    let isNewHighScore = false;
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        document.getElementById('highScore').textContent = highScore;
        isNewHighScore = true;
    }

    // Show game over screen
    document.getElementById('finalScore').textContent = score;
    document.getElementById('newHighScore').style.display = isNewHighScore ? 'block' : 'none';
    document.getElementById('gameOver').style.display = 'block';
}

// Restart game
function restartGame() {
    clearInterval(gameLoop);
    resetGame();
    startGameLoop();
}

// Start game loop
function startGameLoop() {
    gameLoop = setInterval(() => {
        update();
        draw();
    }, gameSpeed);
}

// Start the game
function startGame() {
    draw(); // Initial draw
    startGameLoop();
}

// Initialize game when page loads
window.addEventListener('load', initGame);