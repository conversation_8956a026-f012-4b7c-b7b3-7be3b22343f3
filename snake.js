const GRID_SIZE = 20;
const CANVAS_SIZE = 600;
const INITIAL_SNAKE_LENGTH = 3;

let canvas, ctx;
let snake = [];
let food = {};
let direction = { x: 1, y: 0 };
let gameRunning = false;
let score = 0;
let highScore = 0;
let gameSpeed = 150;
let gameLoop;
let particles = [];
let foodGlow = 0;
let snakeTrail = [];

function initGame() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');

    highScore = localStorage.getItem('snakeHighScore') || 0;
    document.getElementById('highScore').textContent = highScore;

    document.addEventListener('keydown', handleKeyPress);
    document.getElementById('speed').addEventListener('change', changeSpeed);

    resetGame();
    startGame();
}

function resetGame() {
    snake = [];
    const centerX = Math.floor(CANVAS_SIZE / GRID_SIZE / 2);
    const centerY = Math.floor(CANVAS_SIZE / GRID_SIZE / 2);

    for (let i = 0; i < INITIAL_SNAKE_LENGTH; i++) {
        snake.push({ x: centerX - i, y: centerY });
    }

    direction = { x: 1, y: 0 };
    score = 0;
    updateScore();
    generateFood();
    gameRunning = true;

    document.getElementById('gameOver').style.display = 'none';
}

function generateFood() {
    do {
        food = {
            x: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE)),
            y: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE))
        };
    } while (isSnakePosition(food.x, food.y));
}

function isSnakePosition(x, y) {
    return snake.some(segment => segment.x === x && segment.y === y);
}

function update() {
    if (!gameRunning) return;

    const head = { ...snake[0] };
    head.x += direction.x;
    head.y += direction.y;

    if (head.x < 0 || head.x >= CANVAS_SIZE / GRID_SIZE ||
        head.y < 0 || head.y >= CANVAS_SIZE / GRID_SIZE) {
        endGame();
        return;
    }

    if (isSnakePosition(head.x, head.y)) {
        endGame();
        return;
    }

    snake.unshift(head);

    if (head.x === food.x && head.y === food.y) {
        score += 10;
        updateScore();

        for (let i = 0; i < 15; i++) {
            createParticle(
                food.x * GRID_SIZE + GRID_SIZE/2,
                food.y * GRID_SIZE + GRID_SIZE/2,
                `hsl(${Math.random() * 60 + 300}, 100%, 60%)`
            );
        }

        generateFood();

        if (score % 50 === 0 && gameSpeed > 50) {
            gameSpeed = Math.max(50, gameSpeed - 5);
            clearInterval(gameLoop);
            startGameLoop();
        }
    } else {
        const tail = snake.pop();
        createParticle(
            tail.x * GRID_SIZE + GRID_SIZE/2,
            tail.y * GRID_SIZE + GRID_SIZE/2,
            'rgba(0, 255, 100, 0.5)'
        );
    }
}

function createParticle(x, y, color) {
    particles.push({
        x: x,
        y: y,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        life: 1.0,
        color: color,
        size: Math.random() * 3 + 1
    });
}

function updateParticles() {
    particles = particles.filter(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.life -= 0.02;
        particle.size *= 0.98;
        return particle.life > 0;
    });
}

function drawParticles() {
    particles.forEach(particle => {
        ctx.save();
        ctx.globalAlpha = particle.life;
        ctx.fillStyle = particle.color;
        ctx.shadowBlur = 10;
        ctx.shadowColor = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    });
}

function draw() {
    const gradient = ctx.createRadialGradient(CANVAS_SIZE/2, CANVAS_SIZE/2, 0, CANVAS_SIZE/2, CANVAS_SIZE/2, CANVAS_SIZE/2);
    gradient.addColorStop(0, '#0a0a0a');
    gradient.addColorStop(1, '#000000');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
    ctx.lineWidth = 0.5;
    for (let i = 0; i <= CANVAS_SIZE; i += GRID_SIZE) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, CANVAS_SIZE);
        ctx.moveTo(0, i);
        ctx.lineTo(CANVAS_SIZE, i);
        ctx.stroke();
    }

    drawParticles();

    snake.forEach((segment, index) => {
        const x = segment.x * GRID_SIZE;
        const y = segment.y * GRID_SIZE;

        if (index === 0) {
            ctx.save();
            ctx.shadowBlur = 20;
            ctx.shadowColor = '#00ff00';

            const headGradient = ctx.createRadialGradient(x + GRID_SIZE/2, y + GRID_SIZE/2, 0, x + GRID_SIZE/2, y + GRID_SIZE/2, GRID_SIZE/2);
            headGradient.addColorStop(0, '#00ff88');
            headGradient.addColorStop(0.7, '#00cc66');
            headGradient.addColorStop(1, '#008844');
            ctx.fillStyle = headGradient;

            ctx.beginPath();
            ctx.roundRect(x + 2, y + 2, GRID_SIZE - 4, GRID_SIZE - 4, 6);
            ctx.fill();

            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(x + 6, y + 6, 2, 0, Math.PI * 2);
            ctx.arc(x + 14, y + 6, 2, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        } else {
            const alpha = Math.max(0.3, 1 - (index * 0.05));
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.shadowBlur = 10;
            ctx.shadowColor = '#00aa44';

            const bodyGradient = ctx.createRadialGradient(x + GRID_SIZE/2, y + GRID_SIZE/2, 0, x + GRID_SIZE/2, y + GRID_SIZE/2, GRID_SIZE/2);
            bodyGradient.addColorStop(0, '#00cc55');
            bodyGradient.addColorStop(1, '#006622');
            ctx.fillStyle = bodyGradient;

            ctx.beginPath();
            ctx.roundRect(x + 3, y + 3, GRID_SIZE - 6, GRID_SIZE - 6, 4);
            ctx.fill();
            ctx.restore();
        }
    });

    foodGlow += 0.1;
    const glowIntensity = Math.sin(foodGlow) * 0.5 + 0.5;

    ctx.save();
    ctx.shadowBlur = 20 + glowIntensity * 10;
    ctx.shadowColor = '#ff0044';

    const foodX = food.x * GRID_SIZE;
    const foodY = food.y * GRID_SIZE;

    const foodGradient = ctx.createRadialGradient(foodX + GRID_SIZE/2, foodY + GRID_SIZE/2, 0, foodX + GRID_SIZE/2, foodY + GRID_SIZE/2, GRID_SIZE/2);
    foodGradient.addColorStop(0, '#ff4466');
    foodGradient.addColorStop(0.7, '#cc2244');
    foodGradient.addColorStop(1, '#880022');
    ctx.fillStyle = foodGradient;

    ctx.beginPath();
    ctx.arc(foodX + GRID_SIZE/2, foodY + GRID_SIZE/2, (GRID_SIZE/2 - 2) + glowIntensity * 2, 0, Math.PI * 2);
    ctx.fill();

    ctx.fillStyle = '#ffaa88';
    ctx.beginPath();
    ctx.arc(foodX + GRID_SIZE/2 - 2, foodY + GRID_SIZE/2 - 2, 2, 0, Math.PI * 2);
    ctx.fill();

    ctx.restore();

    updateParticles();
}

function handleKeyPress(event) {
    if (!gameRunning) return;

    const key = event.key.toLowerCase();

    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        event.preventDefault();
    }

    switch (key) {
        case 'arrowup':
        case 'w':
            if (direction.y !== 1) direction = { x: 0, y: -1 };
            break;
        case 'arrowdown':
        case 's':
            if (direction.y !== -1) direction = { x: 0, y: 1 };
            break;
        case 'arrowleft':
        case 'a':
            if (direction.x !== 1) direction = { x: -1, y: 0 };
            break;
        case 'arrowright':
        case 'd':
            if (direction.x !== -1) direction = { x: 1, y: 0 };
            break;
    }
}

function changeSpeed() {
    gameSpeed = parseInt(document.getElementById('speed').value);
    if (gameRunning) {
        clearInterval(gameLoop);
        startGameLoop();
    }
}

function updateScore() {
    document.getElementById('score').textContent = score;
}

function endGame() {
    gameRunning = false;
    clearInterval(gameLoop);

    for (let i = 0; i < 30; i++) {
        createParticle(
            snake[0].x * GRID_SIZE + GRID_SIZE/2,
            snake[0].y * GRID_SIZE + GRID_SIZE/2,
            `hsl(${Math.random() * 60}, 100%, 50%)`
        );
    }

    let isNewHighScore = false;
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        document.getElementById('highScore').textContent = highScore;
        isNewHighScore = true;
    }

    document.getElementById('finalScore').textContent = score;
    document.getElementById('newHighScore').style.display = isNewHighScore ? 'block' : 'none';
    document.getElementById('gameOver').style.display = 'block';
}

function restartGame() {
    clearInterval(gameLoop);
    resetGame();
    startGameLoop();
}

function startGameLoop() {
    gameLoop = setInterval(() => {
        update();
        draw();
    }, gameSpeed);
}

function startGame() {
    draw();
    startGameLoop();
}

window.addEventListener('load', initGame);