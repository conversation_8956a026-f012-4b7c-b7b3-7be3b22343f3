const GRID_SIZE = 20;
const CANVAS_SIZE = 600;
const INITIAL_SNAKE_LENGTH = 3;

let canvas, ctx;
let snake = [];
let food = {};
let direction = { x: 1, y: 0 };
let gameRunning = false;
let score = 0;
let highScore = 0;
let gameSpeed = 150;
let gameLoop;
let particles = [];
let foodGlow = 0;
let snakeTrail = [];
let moveProgress = 0;
let lastMoveTime = 0;
let smoothMovement = true;

function initGame() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');

    highScore = localStorage.getItem('snakeHighScore') || 0;
    document.getElementById('highScore').textContent = highScore;

    document.addEventListener('keydown', handleKeyPress);
    document.getElementById('speed').addEventListener('change', changeSpeed);

    resetGame();
    startGame();
}

function resetGame() {
    snake = [];
    const centerX = Math.floor(CANVAS_SIZE / GRID_SIZE / 2);
    const centerY = Math.floor(CANVAS_SIZE / GRID_SIZE / 2);

    for (let i = 0; i < INITIAL_SNAKE_LENGTH; i++) {
        snake.push({
            x: centerX - i,
            y: centerY,
            renderX: centerX - i,
            renderY: centerY
        });
    }

    direction = { x: 1, y: 0 };
    score = 0;
    moveProgress = 0;
    lastMoveTime = 0;
    updateScore();
    generateFood();
    gameRunning = true;

    document.getElementById('gameOver').style.display = 'none';
}

function generateFood() {
    do {
        food = {
            x: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE)),
            y: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE))
        };
    } while (isSnakePosition(food.x, food.y));
}

function isSnakePosition(x, y) {
    return snake.some(segment => segment.x === x && segment.y === y);
}

function update(currentTime) {
    if (!gameRunning) return;

    if (lastMoveTime === 0) lastMoveTime = currentTime;

    const timeSinceLastMove = currentTime - lastMoveTime;
    moveProgress = Math.min(timeSinceLastMove / gameSpeed, 1);

    if (moveProgress >= 1) {
        // Store previous positions before moving
        for (let i = 0; i < snake.length; i++) {
            snake[i].prevX = snake[i].x;
            snake[i].prevY = snake[i].y;
        }

        const head = { ...snake[0] };
        head.x += direction.x;
        head.y += direction.y;
        head.prevX = snake[0].x;
        head.prevY = snake[0].y;
        head.renderX = head.x;
        head.renderY = head.y;

        if (head.x < 0 || head.x >= CANVAS_SIZE / GRID_SIZE ||
            head.y < 0 || head.y >= CANVAS_SIZE / GRID_SIZE) {
            endGame();
            return;
        }

        if (isSnakePosition(head.x, head.y)) {
            endGame();
            return;
        }

        snake.unshift(head);

        if (head.x === food.x && head.y === food.y) {
            score += 10;
            updateScore();

            for (let i = 0; i < 15; i++) {
                createParticle(
                    food.x * GRID_SIZE + GRID_SIZE/2,
                    food.y * GRID_SIZE + GRID_SIZE/2,
                    `hsl(${Math.random() * 60 + 300}, 100%, 60%)`
                );
            }

            generateFood();

            if (score % 50 === 0 && gameSpeed > 50) {
                gameSpeed = Math.max(50, gameSpeed - 5);
            }
        } else {
            const tail = snake.pop();
            createParticle(
                tail.renderX * GRID_SIZE + GRID_SIZE/2,
                tail.renderY * GRID_SIZE + GRID_SIZE/2,
                'rgba(0, 255, 100, 0.5)'
            );
        }

        // Update previous positions for body segments
        for (let i = 1; i < snake.length; i++) {
            if (!snake[i].prevX && !snake[i].prevY) {
                snake[i].prevX = snake[i].x;
                snake[i].prevY = snake[i].y;
            }
        }

        moveProgress = 0;
        lastMoveTime = currentTime;
    }

    updateRenderPositions();
}

function updateRenderPositions() {
    if (!smoothMovement || snake.length === 0) return;

    const easeProgress = easeInOutQuad(moveProgress);

    for (let i = 0; i < snake.length; i++) {
        const segment = snake[i];

        if (i === 0) {
            // Head interpolation
            if (segment.prevX !== undefined && segment.prevY !== undefined) {
                segment.renderX = segment.prevX + (segment.x - segment.prevX) * easeProgress;
                segment.renderY = segment.prevY + (segment.y - segment.prevY) * easeProgress;
            } else {
                segment.renderX = segment.x;
                segment.renderY = segment.y;
            }
        } else {
            // Body segment interpolation
            if (segment.prevX !== undefined && segment.prevY !== undefined) {
                segment.renderX = segment.prevX + (segment.x - segment.prevX) * easeProgress;
                segment.renderY = segment.prevY + (segment.y - segment.prevY) * easeProgress;
            } else {
                segment.renderX = segment.x;
                segment.renderY = segment.y;
            }
        }
    }
}

function easeInOutQuad(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
}

function createParticle(x, y, color) {
    particles.push({
        x: x,
        y: y,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        life: 1.0,
        color: color,
        size: Math.random() * 3 + 1
    });
}

function updateParticles() {
    particles = particles.filter(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.life -= 0.02;
        particle.size *= 0.98;
        return particle.life > 0;
    });
}

function drawParticles() {
    particles.forEach(particle => {
        ctx.save();
        ctx.globalAlpha = particle.life;
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    });
}

function draw() {
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 0.5;
    for (let i = 0; i <= CANVAS_SIZE; i += GRID_SIZE) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, CANVAS_SIZE);
        ctx.moveTo(0, i);
        ctx.lineTo(CANVAS_SIZE, i);
        ctx.stroke();
    }

    drawParticles();

    snake.forEach((segment, index) => {
        const x = segment.renderX * GRID_SIZE;
        const y = segment.renderY * GRID_SIZE;

        if (index === 0) {
            // Snake head with realistic coloring
            const headGradient = ctx.createRadialGradient(x + GRID_SIZE/2, y + GRID_SIZE/3, 0, x + GRID_SIZE/2, y + GRID_SIZE/2, GRID_SIZE/2);
            headGradient.addColorStop(0, '#8FBC8F');  // Light sage green
            headGradient.addColorStop(0.3, '#6B8E23');  // Olive drab
            headGradient.addColorStop(0.7, '#556B2F');  // Dark olive green
            headGradient.addColorStop(1, '#2F4F2F');    // Dark sea green
            ctx.fillStyle = headGradient;

            // Draw head shape (more oval/rounded)
            ctx.beginPath();
            ctx.ellipse(x + GRID_SIZE/2, y + GRID_SIZE/2, GRID_SIZE/2 - 1, GRID_SIZE/2.5 - 1, 0, 0, Math.PI * 2);
            ctx.fill();

            // Add darker outline
            ctx.strokeStyle = '#2F4F2F';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Snake eyes - more realistic
            ctx.fillStyle = '#000000';
            const eyeSize = 2.5;
            const eyeOffset = 4;
            ctx.beginPath();
            ctx.ellipse(x + eyeOffset, y + GRID_SIZE/3, eyeSize, eyeSize * 0.8, 0, 0, Math.PI * 2);
            ctx.ellipse(x + GRID_SIZE - eyeOffset, y + GRID_SIZE/3, eyeSize, eyeSize * 0.8, 0, 0, Math.PI * 2);
            ctx.fill();

            // Eye highlights
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(x + eyeOffset + 1, y + GRID_SIZE/3 - 0.5, 0.8, 0, Math.PI * 2);
            ctx.arc(x + GRID_SIZE - eyeOffset + 1, y + GRID_SIZE/3 - 0.5, 0.8, 0, Math.PI * 2);
            ctx.fill();

            // Nostrils
            ctx.fillStyle = '#1F3F1F';
            ctx.beginPath();
            ctx.arc(x + GRID_SIZE/2 - 1.5, y + GRID_SIZE/2 - 2, 0.5, 0, Math.PI * 2);
            ctx.arc(x + GRID_SIZE/2 + 1.5, y + GRID_SIZE/2 - 2, 0.5, 0, Math.PI * 2);
            ctx.fill();

            // Scale pattern on head
            ctx.strokeStyle = 'rgba(47, 79, 47, 0.3)';
            ctx.lineWidth = 0.5;
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.arc(x + GRID_SIZE/2, y + GRID_SIZE/2 + i * 2, GRID_SIZE/3 - i, 0, Math.PI * 2);
                ctx.stroke();
            }

        } else {
            // Snake body with realistic scales
            const alpha = Math.max(0.6, 1 - (index * 0.02));
            ctx.save();
            ctx.globalAlpha = alpha;

            // Body gradient - more natural snake colors
            const bodyGradient = ctx.createRadialGradient(x + GRID_SIZE/2, y + GRID_SIZE/3, 0, x + GRID_SIZE/2, y + GRID_SIZE/2, GRID_SIZE/2);
            bodyGradient.addColorStop(0, '#9ACD32');    // Yellow green
            bodyGradient.addColorStop(0.3, '#6B8E23');  // Olive drab
            bodyGradient.addColorStop(0.7, '#556B2F');  // Dark olive green
            bodyGradient.addColorStop(1, '#2F4F2F');    // Dark sea green
            ctx.fillStyle = bodyGradient;

            // Draw body segment (circular)
            ctx.beginPath();
            ctx.arc(x + GRID_SIZE/2, y + GRID_SIZE/2, GRID_SIZE/2 - 1, 0, Math.PI * 2);
            ctx.fill();

            // Add body outline
            ctx.strokeStyle = '#2F4F2F';
            ctx.lineWidth = 0.8;
            ctx.stroke();

            // Scale pattern on body
            ctx.strokeStyle = 'rgba(47, 79, 47, 0.2)';
            ctx.lineWidth = 0.3;
            const scaleSize = GRID_SIZE/4;
            for (let sx = 0; sx < 2; sx++) {
                for (let sy = 0; sy < 2; sy++) {
                    ctx.beginPath();
                    ctx.arc(x + scaleSize + sx * scaleSize, y + scaleSize + sy * scaleSize, scaleSize/2, 0, Math.PI * 2);
                    ctx.stroke();
                }
            }

            // Belly stripe (lighter color)
            const bellyGradient = ctx.createLinearGradient(x, y + GRID_SIZE * 0.6, x, y + GRID_SIZE);
            bellyGradient.addColorStop(0, 'rgba(173, 255, 47, 0.3)');
            bellyGradient.addColorStop(1, 'rgba(154, 205, 50, 0.5)');
            ctx.fillStyle = bellyGradient;
            ctx.fillRect(x + 3, y + GRID_SIZE * 0.6, GRID_SIZE - 6, GRID_SIZE * 0.4 - 1);

            ctx.restore();
        }
    });

    const foodX = food.x * GRID_SIZE;
    const foodY = food.y * GRID_SIZE;

    // Draw realistic apple
    const appleRadius = GRID_SIZE/2 - 2;

    // Apple body - main red gradient
    const appleGradient = ctx.createRadialGradient(
        foodX + GRID_SIZE/2 - 2, foodY + GRID_SIZE/2 - 2, 0,
        foodX + GRID_SIZE/2, foodY + GRID_SIZE/2, appleRadius
    );
    appleGradient.addColorStop(0, '#FF6B6B');    // Light red highlight
    appleGradient.addColorStop(0.3, '#E53E3E');  // Medium red
    appleGradient.addColorStop(0.7, '#C53030');  // Dark red
    appleGradient.addColorStop(1, '#9B2C2C');    // Very dark red
    ctx.fillStyle = appleGradient;

    // Draw apple shape (slightly flattened circle)
    ctx.beginPath();
    ctx.ellipse(foodX + GRID_SIZE/2, foodY + GRID_SIZE/2 + 1, appleRadius, appleRadius * 0.9, 0, 0, Math.PI * 2);
    ctx.fill();

    // Apple stem area (indentation at top)
    ctx.fillStyle = '#8B4513';  // Brown
    ctx.beginPath();
    ctx.ellipse(foodX + GRID_SIZE/2, foodY + GRID_SIZE/2 - appleRadius + 2, 2, 1.5, 0, 0, Math.PI * 2);
    ctx.fill();

    // Apple stem
    ctx.fillStyle = '#654321';  // Dark brown
    ctx.fillRect(foodX + GRID_SIZE/2 - 0.5, foodY + GRID_SIZE/2 - appleRadius - 1, 1, 3);

    // Apple leaf
    ctx.fillStyle = '#228B22';  // Forest green
    ctx.beginPath();
    ctx.ellipse(foodX + GRID_SIZE/2 + 2, foodY + GRID_SIZE/2 - appleRadius + 1, 2, 1, Math.PI/4, 0, Math.PI * 2);
    ctx.fill();

    // Apple highlight (makes it look shiny)
    const highlightGradient = ctx.createRadialGradient(
        foodX + GRID_SIZE/2 - 3, foodY + GRID_SIZE/2 - 3, 0,
        foodX + GRID_SIZE/2 - 3, foodY + GRID_SIZE/2 - 3, 4
    );
    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)');
    highlightGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.3)');
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    ctx.fillStyle = highlightGradient;

    ctx.beginPath();
    ctx.ellipse(foodX + GRID_SIZE/2 - 3, foodY + GRID_SIZE/2 - 3, 3, 2, 0, 0, Math.PI * 2);
    ctx.fill();

    // Apple texture lines
    ctx.strokeStyle = 'rgba(139, 69, 19, 0.3)';
    ctx.lineWidth = 0.5;
    for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.arc(foodX + GRID_SIZE/2, foodY + GRID_SIZE/2 + 1, appleRadius - i * 2, Math.PI * 0.1, Math.PI * 0.9);
        ctx.stroke();
    }

    updateParticles();
}

function handleKeyPress(event) {
    if (!gameRunning) return;

    const key = event.key.toLowerCase();

    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        event.preventDefault();
    }

    switch (key) {
        case 'arrowup':
        case 'w':
            if (direction.y !== 1) direction = { x: 0, y: -1 };
            break;
        case 'arrowdown':
        case 's':
            if (direction.y !== -1) direction = { x: 0, y: 1 };
            break;
        case 'arrowleft':
        case 'a':
            if (direction.x !== 1) direction = { x: -1, y: 0 };
            break;
        case 'arrowright':
        case 'd':
            if (direction.x !== -1) direction = { x: 1, y: 0 };
            break;
    }
}

function changeSpeed() {
    gameSpeed = parseInt(document.getElementById('speed').value);
}

function updateScore() {
    document.getElementById('score').textContent = score;
}

function endGame() {
    gameRunning = false;

    for (let i = 0; i < 30; i++) {
        createParticle(
            snake[0].renderX * GRID_SIZE + GRID_SIZE/2,
            snake[0].renderY * GRID_SIZE + GRID_SIZE/2,
            `hsl(${Math.random() * 60}, 100%, 50%)`
        );
    }

    let isNewHighScore = false;
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        document.getElementById('highScore').textContent = highScore;
        isNewHighScore = true;
    }

    document.getElementById('finalScore').textContent = score;
    document.getElementById('newHighScore').style.display = isNewHighScore ? 'block' : 'none';
    document.getElementById('gameOver').style.display = 'block';
}

function restartGame() {
    gameRunning = false;
    resetGame();
    startGameLoop();
}

function startGameLoop() {
    function gameLoopFrame(currentTime) {
        if (gameRunning) {
            update(currentTime);
            draw();
            requestAnimationFrame(gameLoopFrame);
        }
    }
    requestAnimationFrame(gameLoopFrame);
}

function startGame() {
    draw();
    startGameLoop();
}

window.addEventListener('load', initGame);